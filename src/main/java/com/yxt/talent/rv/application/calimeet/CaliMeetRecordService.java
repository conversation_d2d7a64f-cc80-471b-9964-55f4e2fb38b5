package com.yxt.talent.rv.application.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.ApplicationCommandService;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.spsdk.common.utils.IArrayUtils;
import com.yxt.talent.rv.application.calimeet.dto.CaliRecordDimExportDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliRecordUserDTO;
import com.yxt.talent.rv.application.calimeet.impt.CaliRecordDimLevelStrategy;
import com.yxt.talent.rv.application.calimeet.impt.CaliRecordDimResultStrategy;
import com.yxt.talent.rv.application.calimeet.impt.CaliRecordIndResultStrategy;
import com.yxt.talent.rv.application.calimeet.impt.CaliXpdRecordExportStrategy;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import com.yxt.talent.rv.application.xpd.common.enums.GridCellTemplateEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliRecord4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetRecordVO;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.CaliMeetTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimCalcTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimResultTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordItemPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericApaasFileExportVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ApplicationCommandService
public class CaliMeetRecordService {
    private final CalimeetMapper calimeetMapper;
    private final CalimeetRecordItemMapper calimeetRecordItemMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdGridCellMapper gridCellMapper;
    private final XpdGridMapper xpdGridMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final I18nComponent i18nComponent;
    private final CalimeetResultUserDimMapper resultUserDimMapper;
    private final XpdGridLevelMapper gridLevelMapper;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdMapper xpdMapper;
    private final AuthService authService;
    private final MessageSourceService msgSource;
    private final XpdResultCalcService xpdResultCalcService;
    private final CalimeetResultUserIndicatorMapper resultUserIndicatorMapper;
    private final DlcComponent dlcComponent;
    private final CaliRecordDimLevelStrategy caliRecordDimLevelStrategy;
    private final CaliRecordDimResultStrategy caliRecordDimResultStrategy;
    private final CaliRecordIndResultStrategy caliRecordIndResultStrategy;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetResultUserIndicatorMapper calimeetResultUserIndicatorMapper;
    private final CalimeetResultUserMapper calimeetResultUserMapper;
    private final CaliXpdRecordExportStrategy caliXpdRecordExportStrategy;
    private final CalimeetUserMapper calimeetUserMapper;


    private static final String[] HEADER1_KEYS = {"fullname", "username", "status", "deptName", "positionName",
            "gradeName", "dimCombName", "caliShiftStr"};

    private static final String[] HEADER2_KEYS = {"caliUserName", "caliTime", "reason", "suggestion"};

    public PagingList<CaliMeetRecordVO> list(String orgId, CaliRecord4Query query) {
        query.setSearchKey(SqlUtil.escapeSql(query.getSearchKey()));
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        IPage<CaliMeetRecordVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        String xpdId = query.getXpdId();
        XpdGridPO xpdGrid = xpdGridMapper.selectByXpdId(orgId, xpdId);

        if (StringUtils.isNotEmpty(query.getDimId())) {
            String dimId = query.getDimId();
            List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.selectByDimIds(orgId, Lists.newArrayList(dimId));
            query.setDimCombIds(xpdDimCombs.stream().map(XpdDimCombPO::getId).toList());
        }

        IPage<CaliMeetRecordVO> resultPage = calimeetRecordItemMapper.pageQuery(page, orgId, query);
        List<CaliMeetRecordVO> records = resultPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
            return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
        }
        List<String> dimCombIds = records.stream().map(CaliMeetRecordVO::getDimCombId).toList();
        List<XpdDimCombPO> xpdDimCombList = xpdDimCombMapper.selectByIds(dimCombIds);
        Map<String, String> dimCombMap = StreamUtil.list2map(xpdDimCombList, XpdDimCombPO::getId,
                XpdDimCombPO::getCombName);
        // 宫格名称
        Integer configType = xpdGrid.getConfigType();

        List<XpdGridCellPO> xpdGridCellList = gridCellMapper.listByGridId(orgId, xpdGrid.getId());
        Map<String, List<XpdGridCellPO>> gridCellMap = xpdGridCellList.stream()
                .collect(Collectors.groupingBy(XpdGridCellPO::getDimCombId));

        List<String> caliUserIds = records.stream().map(CaliMeetRecordVO::getCaliUserId).toList();
        List<UdpLiteUserPO> udpLiteUserList = udpLiteUserMapper.selectByUserIds(orgId, caliUserIds);
        Map<String, String> userMap = StreamUtil.list2map(udpLiteUserList, UdpLiteUserPO::getId,
                UdpLiteUserPO::getFullname);

        // 校准会名称
        List<String> caliMeetIds = records.stream().map(CaliMeetRecordVO::getCaliMeetId).toList();
        List<CalimeetPO> calimeetList = calimeetMapper.selectByOrgIdAndIds(orgId, caliMeetIds);
        Map<String, String> caliMeetMap = StreamUtil.list2map(calimeetList, CalimeetPO::getId,
                CalimeetPO::getCalimeetName);

        for (CaliMeetRecordVO record : records) {
            // 维度组合
            record.setDimCombName(dimCombMap.getOrDefault(record.getDimCombId(), ""));
            record.setOriginalCellIndexName(
                    findCellName(gridCellMap, configType, record.getDimCombId(), record.getOriginalCellIndex()));
            record.setCellIndexName(
                    findCellName(gridCellMap, configType, record.getDimCombId(), record.getCellIndex()));
            record.setCaliUserName(userMap.getOrDefault(record.getCaliUserId(), ""));

            if (record.getDeleted() == 1) {
                record.setStatus(2);
            } else if (record.getStatus() == 1) {
                record.setStatus(1);
            } else if (record.getStatus() == 0) {
                record.setStatus(0);
            }

            // 校准会名称
            record.setCaliMeetName(caliMeetMap.getOrDefault(record.getCaliMeetId(), ""));

        }
        return BeanCopierUtil.toPagingList(resultPage);
    }

    private String findCellName(Map<String, List<XpdGridCellPO>> gridCellMap, Integer configType, String dimCombId,
            Integer cellIndex) {
        if (configType == 0) {
            dimCombId = "";
        }
        List<XpdGridCellPO> xpdGridCellList = gridCellMap.get(dimCombId);
        if (CollectionUtils.isEmpty(xpdGridCellList)) {
            return "";
        }
        for (XpdGridCellPO xpdGridCell : xpdGridCellList) {
            if (Objects.equals(cellIndex, xpdGridCell.getCellIndex())) {
                return xpdGridCell.getCellName();
            }
        }
        return "";

    }


    /**
     * 校准会内-校准记录导出
     *
     * @param orgId
     * @param query
     * @return
     */
    public GenericApaasFileExportVO export(String orgId, CaliRecord4Query query) {

        String caliMeetId = query.getCaliMeetId();
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);

        XpdPO xpdPO = xpdMapper.selectById(calimeet.getXpdId());
        Validate.isNotNull(xpdPO, ExceptionKeys.XPD_NOT_EXIST);
        // 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
        Integer calimeetType = calimeet.getCalimeetType();
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, calimeet.getXpdId());

        if (StringUtils.isNotEmpty(query.getDimId())) {
            String dimId = query.getDimId();
            List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.selectByDimIds(orgId, Lists.newArrayList(dimId));
            query.setDimCombIds(xpdDimCombs.stream().map(XpdDimCombPO::getId).toList());
        }

        // 盘点结果类型:0-分值 1-达标率
        Integer resultType = xpdRuleConf.getResultType();

        //
        // 维度分层
        List<CaliRecordUserDTO> caliMeetRecordList = calimeetRecordItemMapper.listAll(orgId, query);
        String path = "";
        // 维度分层
        if (calimeetType == 0) {
            DynamicExcelExportContent content = covertDimLevel(caliMeetRecordList, orgId, xpdPO.getId(), caliMeetId,
                    calimeetType, resultType, "sheet1");
            String fileName = getFileName("apis.sptalentrv.cali.record.dim.level");
            long taskId = dlcComponent.prepareExport(fileName, caliRecordDimLevelStrategy);
            path = dlcComponent.upload2Disk(fileName, content, caliRecordDimLevelStrategy, taskId);
        } else if (calimeetType == 1) {
            //维度结果
            DynamicExcelExportContent content = covertDimLevel(caliMeetRecordList, orgId, xpdPO.getId(), caliMeetId,
                    calimeetType, resultType, "sheet1");
            String fileName = getFileName("apis.sptalentrv.cali.record.dim.result");
            long taskId = dlcComponent.prepareExport(fileName, caliRecordDimResultStrategy);
            path = dlcComponent.upload2Disk(fileName, content, caliRecordDimResultStrategy, taskId);
        } else {
            DynamicExcelExportContent content = covertInditor(caliMeetRecordList, orgId, calimeet.getXpdId(),
                    calimeet.getId(), resultType, "sheet1");
            String fileName = getFileName("apis.sptalentrv.cali.record.ind.result");
            long taskId = dlcComponent.prepareExport(fileName, caliRecordIndResultStrategy);
            path = dlcComponent.upload2Disk(fileName, content, caliRecordIndResultStrategy, taskId);
        }
        GenericApaasFileExportVO res = new GenericApaasFileExportVO();
        res.setFilePath(path);
        return res;
    }

    public GenericApaasFileExportVO xpdExport(String orgId, CaliRecord4Query query) {

        String caliMeetId = query.getCaliMeetId();
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);

        XpdPO xpdPO = xpdMapper.selectById(calimeet.getXpdId());
        Validate.isNotNull(xpdPO, ExceptionKeys.XPD_NOT_EXIST);

        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, calimeet.getXpdId());

        if (StringUtils.isNotEmpty(query.getDimId())) {
            String dimId = query.getDimId();
            List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.selectByDimIds(orgId, Lists.newArrayList(dimId));
            query.setDimCombIds(xpdDimCombs.stream().map(XpdDimCombPO::getId).toList());
        }
        // 盘点结果类型:0-分值 1-达标率
        Integer resultType = xpdRuleConf.getResultType();

        // 维度分层

        // 找出所盘点下所有的校准会
        List<CalimeetPO> calimeetList = calimeetMapper.selectListByXpdIdAndOrgId(xpdPO.getId(), orgId);
        List<String> caliMeetIds = calimeetList.stream().map(CalimeetPO::getId).toList();
        // 和query中的校准会id取交集
        List<String> realCaliMeetIds = CommonUtil.getIntersectionOrList(caliMeetIds, query.getCaliMeetIds());

        DynamicExcelExportContent allContent = handleXpdCali(orgId, query, realCaliMeetIds, caliMeetId, calimeet, xpdPO,
                resultType);
        String fileName = getFileName("apis.sptalentrv.cali.xpd.record.filename");
        // 导出
        long taskId = dlcComponent.prepareExport(fileName, caliXpdRecordExportStrategy);
        String path = dlcComponent.upload2Disk(fileName, allContent, caliXpdRecordExportStrategy, taskId);
        GenericApaasFileExportVO res = new GenericApaasFileExportVO();
        res.setFileUrl(path);
        res.setFilePath(path);
        return res;

    }


    private DynamicExcelExportContent handleXpdCali(String orgId, CaliRecord4Query query, List<String> realCaliMeetIds,
            String caliMeetId, CalimeetPO calimeet, XpdPO xpdPO, Integer resultType) {
        DynamicExcelExportContent allContent = new DynamicExcelExportContent();
        int index = 1;
        for (String realCaliMeetId : realCaliMeetIds) {
            query.setCaliMeetId(realCaliMeetId);
            List<CaliRecordUserDTO> caliMeetRecordList = calimeetRecordItemMapper.listAll(orgId, query);
            CalimeetPO tempCalimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
            if (tempCalimeet == null) {
                continue;
            }
            String sheetName = "sheet" + index;
            DynamicExcelExportContent content = new DynamicExcelExportContent();
            // 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
            Integer calimeetType = calimeet.getCalimeetType();
            if (calimeetType == 0) {
                content = covertDimLevel(caliMeetRecordList, orgId, xpdPO.getId(), caliMeetId, calimeetType, resultType,
                        sheetName);
            } else if (calimeetType == 1) {
                //维度结果
                content = covertDimLevel(caliMeetRecordList, orgId, xpdPO.getId(), caliMeetId, calimeetType, resultType,
                        sheetName);
            } else {
                content = covertInditor(caliMeetRecordList, orgId, calimeet.getXpdId(), calimeet.getId(), resultType,
                        sheetName);
            }
            Map<String, List<List<String>>> headers = allContent.getHeaders();
            if (headers.isEmpty()) {
                Map<String, List<List<String>>> newHeaders = new HashMap<>();
                newHeaders.putAll(content.getHeaders());
                allContent.setHeaders(newHeaders);
            } else {
                headers.putAll(content.getHeaders());
            }
            List<IdName> sheets = allContent.getSheets();
            if (CollectionUtils.isEmpty(sheets)) {
                List<IdName> newSheets = new ArrayList<>();
                newSheets.addAll(content.getSheets());
                allContent.setSheets(newSheets);
            } else {
                sheets.addAll(content.getSheets());
            }
            Map<String, List<Object>> data = allContent.getData();
            if (data.isEmpty()) {
                Map<String, List<Object>> newData = new HashMap<>();
                newData.putAll(content.getData());
                allContent.setData(newData);
            } else {
                data.putAll(content.getData());
            }
            index++;
        }
        return allContent;
    }

    private String getFileName(String param) {
        String fileName = i18nComponent.getI18nValue(param);
        return fileName + "_" + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;
    }

    private DynamicExcelExportContent covertInditor(List<CaliRecordUserDTO> caliMeetRecordList, String orgId,
            String xpdId,

            String calimeetId, Integer resultType, String sheetName) {
        DynamicExcelExportContent content = new DynamicExcelExportContent();
        List<IdName> sheets = new ArrayList<>();
        IdName idName = new IdName();
        idName.setId(sheetName);
        idName.setName(sheetName);
        sheets.add(idName);


        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        Map<String, String> sdDimNameMap = fullDto.getSdDimNameMap();
        List<XpdDimRule4Cali> ruleList = fullDto.getRuleList().stream()
                .filter(rule -> rule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE && !DimCalcTypeEnum.isPerf(
                        rule.getCalcType()) && CollectionUtils.isNotEmpty(rule.getFormulaSdIndicatorSet())).toList();

        Map<String, XpdSdIndicatorScoreDto> sdIndicatorMap = fullDto.getSdIndicatorMap();

        // 动态表头
        Map<String, List<List<String>>> header = initHeader(ruleList, sdDimNameMap, sdIndicatorMap, sheetName);

        // excel值
        Map<String, List<Object>> data = initData(caliMeetRecordList, orgId, xpdId, calimeetId, resultType, ruleList,
                sheetName);
        content.setSheets(sheets);
        content.setHeaders(header);
        content.setData(data);
        return content;
    }

    private Map<String, List<Object>> initData(List<CaliRecordUserDTO> caliMeetRecordList, String orgId, String xpdId,
            String calimeetId, Integer resultType, List<XpdDimRule4Cali> ruleList, String sheetName) {
        Map<String, List<Object>> dataMap = new HashMap<>();

        List<Object> datas = new ArrayList<>();
        String deletedStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.deleted");
        String enableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.enable");
        String disableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.disable");

        List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimCombPO> dimCombMap = StreamUtil.list2map(xpdDimCombs, XpdDimCombPO::getId);

        List<CalimeetResultUserIndicatorPO> resultUserIndicators = resultUserIndicatorMapper.selectByOrgIdAndCalimeetId(
                orgId, calimeetId);

        Map<String, List<CalimeetResultUserIndicatorPO>> indicatorResultMap = resultUserIndicators.stream()
                .collect(Collectors.groupingBy(CalimeetResultUserIndicatorPO::getUserId));

        List<String> userIds = caliMeetRecordList.stream().map(CaliMeetRecordVO::getCaliUserId).distinct().toList();
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);
        Map<String, String> userNameMap = StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getId,
                UdpLiteUserPO::getFullname);

        for (CaliRecordUserDTO caliRecordUser : caliMeetRecordList) {
            List<String> dataList = new ArrayList<>();
            dataList.add(caliRecordUser.getFullname());
            dataList.add(caliRecordUser.getUsername());
            // 状态
            Integer status = caliRecordUser.getStatus();
            Integer deleted = caliRecordUser.getDeleted();
            if (deleted == 1) {
                dataList.add(deletedStr);
            } else if (status == 1) {
                dataList.add(enableStr);
            } else {
                dataList.add(disableStr);
            }
            dataList.add(caliRecordUser.getDeptName());
            dataList.add(caliRecordUser.getPositionName());
            dataList.add(caliRecordUser.getGradeName());
            String dimCombId = caliRecordUser.getDimCombId();
            XpdDimCombPO xpdDimComb = dimCombMap.get(dimCombId);
            if (xpdDimComb != null) {
                dataList.add(xpdDimComb.getCombName());
            } else {
                dataList.add("");
            }

            dataList.add(String.valueOf(caliRecordUser.getCaliShift()));
            Map<String, CaliUpdateUserResultDto> indicatorMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(caliRecordUser.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                            .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null),
                    CaliUpdateUserResultDto::getSdIndicatorId, item -> item);
            // 校准前指标数据

            String xSdDimId = xpdDimComb.getXSdDimId();
            String ySdDimId = xpdDimComb.getYSdDimId();
            for (XpdDimRule4Cali dimRule : ruleList) {
                String sdDimId = dimRule.getSdDimId();

                Set<String> formulaSdIndicatorSet = dimRule.getFormulaSdIndicatorSet();
                for (String indicatorId : formulaSdIndicatorSet) {

                    // 校准前后两个格子
                    if (xSdDimId.equals(sdDimId) || ySdDimId.equals(sdDimId)) {
                        // 校准前
                        dataList.add(
                                findIndicatorResult(indicatorResultMap.get(caliRecordUser.getUserId()), indicatorId,
                                        resultType));
                        // 校准后
                        CaliUpdateUserResultDto caliUpdateUserResultDto = indicatorMap.get(indicatorId);
                        dataList.add(findAfterIndicator(caliUpdateUserResultDto, resultType));

                    } else {
                        // 都不匹配
                        dataList.add("");
                        dataList.add("");
                    }
                }
            }

            // 最后数据补全
            // 校准人
            dataList.add(userNameMap.getOrDefault(caliRecordUser.getUserId(), ""));
            // 校准时间
            dataList.add(FastDateFormat.getInstance("yyyy-MM-dd HH:mm:dd").format(caliRecordUser.getCaliTime()));
            datas.add(dataList);
        }
        dataMap.put(sheetName, datas);
        return dataMap;
    }

    private String findAfterIndicator(CaliUpdateUserResultDto caliUpdateUserResultDto, Integer resultType) {
        if (caliUpdateUserResultDto == null) {
            return "";
        }
        if (resultType == 0) {
            return String.valueOf(caliUpdateUserResultDto.getScoreValue());
        } else {
            return caliUpdateUserResultDto.getQualified() + "%";
        }

    }

    private String findIndicatorResult(List<CalimeetResultUserIndicatorPO> calimeetResultUserIndicators,
            String indicatorId, Integer resultType) {
        if (CollectionUtils.isEmpty(calimeetResultUserIndicators)) {
            return "";
        }
        for (CalimeetResultUserIndicatorPO result : calimeetResultUserIndicators) {
            if (indicatorId.equals(result.getSdIndicatorId())) {
                if (resultType == 0) {
                    return String.valueOf(result.getScoreValue());
                } else {
                    return result.getQualified() + "%";
                }
            }
        }

        return "";

    }

    private Map<String, List<List<String>>> initHeader(List<XpdDimRule4Cali> ruleList, Map<String, String> sdDimNameMap,
            Map<String, XpdSdIndicatorScoreDto> sdIndicatorMap, String sheetName) {
        Map<String, List<List<String>>> headerMap = new HashMap<>();
        List<List<String>> headers = new ArrayList<>();
        Locale locale = authService.getLocale();

        for (String key : HEADER1_KEYS) {
            String header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.cali.record.user." + key, locale);
            List<String> headerList = new ArrayList<>();
            headerList.add(header);
            headerList.add(header);
            headerList.add(header);
            headers.add(headerList);
        }

        // 动态头部

        List<String> caliActions = new ArrayList<>();
        caliActions.add(i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.before"));
        caliActions.add(i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.after"));
        for (XpdDimRule4Cali dimRule : ruleList) {
            // 维度名称
            String sdDimId = dimRule.getSdDimId();
            String dimName = sdDimNameMap.getOrDefault(sdDimId, "");
            Set<String> indicatorIds = dimRule.getFormulaSdIndicatorSet();
            // 校准前
            for (String indicatorId : indicatorIds) {
                XpdSdIndicatorScoreDto xpdSdIndicatorScoreDto = sdIndicatorMap.get(indicatorId);
                String indicatorName = "";
                if (xpdSdIndicatorScoreDto != null) {
                    indicatorName = xpdSdIndicatorScoreDto.getName();
                }
                for (String caliAction : caliActions) {
                    List<String> headerList = new ArrayList<>();
                    headerList.add(dimName);
                    headerList.add(indicatorName);
                    headerList.add(caliAction);
                    headers.add(headerList);
                }
            }
        }
        // 固定头部2
        for (String key : HEADER2_KEYS) {
            String header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.cali.record.user." + key, locale);
            List<String> headerList = new ArrayList<>();
            headerList.add(header);
            headerList.add(header);
            headerList.add(header);
            headers.add(headerList);
        }
        headerMap.put(sheetName, headers);

        return headerMap;
    }


    private List<CaliRecordDimExportDTO> covertDimResult(List<CaliRecordUserDTO> caliMeetRecordList, String orgId,
            String xpdId, CalimeetPO calimeet, Integer resultType) {
        List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimCombPO> dimCombMap = StreamUtil.list2map(xpdDimCombs, XpdDimCombPO::getId);

        List<XpdGridCellPO> xpdGridCellList = gridCellMapper.listByGridId(orgId, xpdId);
        Map<String, List<XpdGridCellPO>> gridCellMap = xpdGridCellList.stream()
                .collect(Collectors.groupingBy(XpdGridCellPO::getCellColor));

        XpdGridPO xpdGrid = xpdGridMapper.selectByXpdId(orgId, xpdId);
        Integer configType = xpdGrid.getConfigType();

        List<CalimeetResultUserDimPO> resultUserDims = resultUserDimMapper.listByOrgIdAndCalimeetId(orgId,
                calimeet.getId());
        Map<String, List<CalimeetResultUserDimPO>> userDimMap = resultUserDims.stream()
                .collect(Collectors.groupingBy(CalimeetResultUserDimPO::getUserId));

        List<String> userIds = caliMeetRecordList.stream().map(CaliMeetRecordVO::getCaliUserId).distinct().toList();
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);
        Map<String, String> userNameMap = StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getId,
                UdpLiteUserPO::getFullname);

        List<CaliRecordDimExportDTO> resultList = new ArrayList<>();
        String deletedStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.deleted");
        String enableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.enable");
        String disableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.disable");
        for (CaliRecordUserDTO recordDimResult : caliMeetRecordList) {
            CaliRecordDimExportDTO export = new CaliRecordDimExportDTO();
            BeanCopierUtil.copy(recordDimResult, export);
            if (recordDimResult.getDeleted() == 1) {
                export.setStatusStr(deletedStr);
            } else if (recordDimResult.getStatus() == 1) {
                export.setStatusStr(enableStr);
            } else if (recordDimResult.getStatus() == 0) {
                export.setStatusStr(disableStr);
            }
            // 维度组合
            XpdDimCombPO xpdDimComb = dimCombMap.get(recordDimResult.getDimCombId());
            if (xpdDimComb == null) {
                continue;
            }
            export.setDimCombName(xpdDimComb.getCombName());
            //
            export.setCaliShiftStr(String.valueOf(recordDimResult.getCaliShift()));
            export.setOriginalCellIndexName(findCellName(gridCellMap, configType, recordDimResult.getDimCombId(),
                    recordDimResult.getOriginalCellIndex()));
            export.setCellIndexName(findCellName(gridCellMap, configType, recordDimResult.getDimCombId(),
                    recordDimResult.getCellIndex()));

            Map<String, CaliUpdateUserResultDto> dimMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(recordDimResult.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                            .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdDimId,
                    item -> item);
            // 校准前x维度
            export.setOriginalDimLevel1(
                    findDimResult(userDimMap.get(recordDimResult.getUserId()), xpdDimComb.getXSdDimId(), resultType));
            // 校准后x维度
            export.setDimLevel1(findDimAfterResult(dimMap.get(xpdDimComb.getXSdDimId()), resultType));

            // 校准前y维度
            export.setOriginalDimLevel2(
                    findDimResult(userDimMap.get(recordDimResult.getUserId()), xpdDimComb.getYSdDimId(), resultType));
            // 校准后y维度
            export.setDimLevel2(findDimAfterResult(dimMap.get(xpdDimComb.getYSdDimId()), resultType));

            export.setCaliUserName(userNameMap.getOrDefault(recordDimResult.getUserId(), ""));
            export.setCaliTimeStr(
                    FastDateFormat.getInstance("yyyy-MM-dd HH:mm:dd").format(recordDimResult.getCaliTime()));
            export.setReason(recordDimResult.getReason());
            export.setSuggestion(recordDimResult.getSuggestion());
            resultList.add(export);
        }

        return resultList;
    }

    private String findDimAfterResult(CaliUpdateUserResultDto resultDto, Integer resultType) {
        if (resultDto == null) {
            return "";
        }
        // 盘点结果类型:0-分值 1-达标率
        if (resultType == 0) {
            return String.valueOf(resultDto.getScoreValue());
        } else {
            return resultDto.getQualifiedPtg() + "%";
        }

    }

    private String findDimResult(List<CalimeetResultUserDimPO> resultUserDims, String dimId, Integer resultType) {
        if (CollectionUtils.isEmpty(resultUserDims)) {
            return "";
        }
        // 盘点结果类型:0-分值 1-达标率
        for (CalimeetResultUserDimPO resultUserDim : resultUserDims) {
            if (dimId.equals(resultUserDim.getSdDimId())) {
                if (resultType == 0) {
                    return String.valueOf(resultUserDim.getScoreValue());
                } else {
                    return resultUserDim.getQualifiedPtg() + "%";
                }
            }
        }
        return "";
    }

    private DynamicExcelExportContent covertDimLevel(List<CaliRecordUserDTO> caliMeetRecordList, String orgId,
            String xpdId, String caliMeetId, Integer calimeetType, Integer resultType, String sheetName) {
        // 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
        DynamicExcelExportContent content = new DynamicExcelExportContent();
        List<IdName> sheets = new ArrayList<>();
        IdName idName = new IdName();
        idName.setId(sheetName);
        idName.setName(sheetName);
        sheets.add(idName);

        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        List<XpdDimRule4Cali> ruleList = fullDto.getRuleList();
        Map<String, String> sdDimNameMap = fullDto.getSdDimNameMap();

        // 头部
        Map<String, List<List<String>>> headMap = initDimHead(ruleList, sdDimNameMap, sheetName);

        // 数据
        Map<String, List<Object>> dataMap = initDimData(caliMeetRecordList, orgId, xpdId, caliMeetId, calimeetType,
                resultType, ruleList, sheetName);
        content.setSheets(sheets);
        content.setHeaders(headMap);
        content.setData(dataMap);
        return content;
    }

    @NotNull
    private Map<String, List<Object>> initDimData(List<CaliRecordUserDTO> caliMeetRecordList, String orgId,
            String xpdId, String caliMeetId, Integer calimeetType, Integer resultType, List<XpdDimRule4Cali> ruleList,
            String sheetName) {
        List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimCombPO> dimCombMap = StreamUtil.list2map(xpdDimCombs, XpdDimCombPO::getId);

        List<XpdGridCellPO> xpdGridCellList = gridCellMapper.listByGridId(orgId, xpdId);
        Map<String, List<XpdGridCellPO>> gridCellMap = xpdGridCellList.stream()
                .collect(Collectors.groupingBy(XpdGridCellPO::getCellColor));

        XpdGridPO xpdGrid = xpdGridMapper.selectByXpdId(orgId, xpdId);
        Integer configType = xpdGrid.getConfigType();

        List<CalimeetResultUserDimPO> resultUserDims = resultUserDimMapper.listByOrgIdAndCalimeetId(orgId, caliMeetId);
        Map<String, List<CalimeetResultUserDimPO>> userDimMap = resultUserDims.stream()
                .collect(Collectors.groupingBy(CalimeetResultUserDimPO::getUserId));

        List<XpdGridLevelPO> gridLevels = gridLevelMapper.listByXpdId(orgId, xpdId);
        Map<String, String> levelNameMap = StreamUtil.list2map(gridLevels, XpdGridLevelPO::getId,
                XpdGridLevelPO::getLevelName);

        List<String> userIds = caliMeetRecordList.stream().map(CaliMeetRecordVO::getCaliUserId).distinct().toList();
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);
        Map<String, String> userNameMap = StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getId,
                UdpLiteUserPO::getFullname);

        List<CaliRecordDimExportDTO> resultList = new ArrayList<>();
        String deletedStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.deleted");
        String enableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.enable");
        String disableStr = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.disable");

        // 内容

        List<Object> datas = new ArrayList<>();
        for (CaliRecordUserDTO recordDimLevel : caliMeetRecordList) {
            List<String> headerList = new ArrayList<>();
            headerList.add(recordDimLevel.getFullname());
            headerList.add(recordDimLevel.getUsername());
            if (recordDimLevel.getDeleted() == 1) {
                headerList.add(deletedStr);
            } else if (recordDimLevel.getStatus() == 1) {
                headerList.add(enableStr);
            } else if (recordDimLevel.getStatus() == 0) {
                headerList.add(disableStr);
            }

            headerList.add(recordDimLevel.getDeptName());
            headerList.add(recordDimLevel.getPositionName());
            headerList.add(recordDimLevel.getGradeName());
            // 维度组合
            XpdDimCombPO xpdDimComb = dimCombMap.get(recordDimLevel.getDimCombId());
            String combName = xpdDimComb.getCombName();
            headerList.add(combName);
            headerList.add(String.valueOf(recordDimLevel.getCaliShift()));
            // 校准前宫格
            headerList.add(findCellName(gridCellMap, configType, recordDimLevel.getDimCombId(),
                    recordDimLevel.getOriginalCellIndex()));
            // 校准后宫格
            headerList.add(findCellName(gridCellMap, configType, recordDimLevel.getDimCombId(),
                    recordDimLevel.getCellIndex()));

            String xSdDimId = xpdDimComb.getXSdDimId();
            String ySdDimId = xpdDimComb.getYSdDimId();
            for (XpdDimRule4Cali dimRule : ruleList) {
                String sdDimId = dimRule.getSdDimId();
                if (xSdDimId.equals(sdDimId) || ySdDimId.equals(ySdDimId)) {
                    // 校准前
                    // 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
                    if (calimeetType == 0) {
                        headerList.add(findUserDimLevelName(userDimMap.get(recordDimLevel.getUserId()),
                                xpdDimComb.getXSdDimId(), levelNameMap));
                    } else if (calimeetType == 1) {
                        headerList.add(
                                findDimResult(userDimMap.get(recordDimLevel.getUserId()), xpdDimComb.getXSdDimId(),
                                        resultType));
                    } else {
                        headerList.add("");
                    }
                    // 校准后
                    Map<String, CaliUpdateUserResultDto> dimLevelMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(recordDimLevel.getCaliDetails(),
                                            CaliUpdateUserResultWrapDto.class)).map(CaliUpdateUserResultWrapDto::getUserResults)
                            .orElse(null), CaliUpdateUserResultDto::getSdDimId, item -> item);
                    if (calimeetType == 0) {
                        headerList.add(findAfterLevelName(dimLevelMap, xpdDimComb.getXSdDimId(), levelNameMap));
                    } else if (calimeetType == 1) {
                        headerList.add(findDimAfterResult(dimLevelMap.get(xpdDimComb.getXSdDimId()), resultType));
                    } else {
                        headerList.add("");
                    }

                } else {
                    headerList.add("");
                    headerList.add("");
                }
            }
            // 校准人
            headerList.add(userNameMap.getOrDefault(recordDimLevel.getCaliUserId(), ""));
            headerList.add(FastDateFormat.getInstance("yyyy-MM-dd HH:mm:dd").format(recordDimLevel.getCaliTime()));
            headerList.add(recordDimLevel.getReason());
            headerList.add(recordDimLevel.getSuggestion());
            datas.add(headerList);
        }
        Map<String, List<Object>> dataMap = new HashMap<>();
        dataMap.put(sheetName, datas);
        return dataMap;
    }

    private Map<String, List<List<String>>> initDimHead(List<XpdDimRule4Cali> ruleList,
            Map<String, String> sdDimNameMap, String sheetName) {
        String caliBefore = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.before");
        String caliAfter = i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.after");

        Map<String, List<List<String>>> headerMap = new HashMap<>();
        Locale locale = authService.getLocale();
        List<List<String>> headers = new ArrayList<>();
        for (String key : HEADER1_KEYS) {
            String header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.cali.record.user." + key, locale);
            List<String> headerList = new ArrayList<>();
            headerList.add(header);
            headers.add(headerList);
        }
        // 校准前宫格
        headers.add(Lists.newArrayList(i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.before.cell")));
        // 校后前宫格
        headers.add(Lists.newArrayList(i18nComponent.getI18nValue("apis.sptalentrv.cali.record.user.after.cell")));
        // 维度数据

        for (XpdDimRule4Cali dimRule : ruleList) {
            String sdDimId = dimRule.getSdDimId();
            String dimName = sdDimNameMap.getOrDefault(sdDimId, "");
            headers.add(Lists.newArrayList(caliBefore + dimName));
            headers.add(Lists.newArrayList(caliAfter + dimName));
        }


        for (String key : HEADER2_KEYS) {
            String header = ApiUtil.getL10nString(msgSource, "apis.sptalentrv.cali.record.user." + key, locale);
            List<String> headerList = new ArrayList<>();
            headerList.add(header);
            headers.add(headerList);
        }
        headerMap.put(sheetName, headers);
        return headerMap;
    }

    private String findAfterLevelName(Map<String, CaliUpdateUserResultDto> dimLevelMap, String dimId,
            Map<String, String> levelNameMap) {
        CaliUpdateUserResultDto caliUpdateUserResultDto = dimLevelMap.get(dimId);
        if (caliUpdateUserResultDto == null) {
            return "";
        }
        String gridLevelId = caliUpdateUserResultDto.getGridLevelId();
        return levelNameMap.getOrDefault(gridLevelId, "");
    }

    private String findUserDimLevelName(List<CalimeetResultUserDimPO> resultUserDims, String dimId,
            Map<String, String> levelNameMap) {
        for (CalimeetResultUserDimPO resultUserDim : resultUserDims) {
            if (dimId.equals(resultUserDim.getSdDimId())) {
                return levelNameMap.getOrDefault(resultUserDim.getGridLevelId(), "");
            }
        }
        return "";
    }

    public CaliDimResultResp queryCaliResult(String orgId, String caliMeetId, String id) {
        CalimeetRecordItemPO calimeetRecordItem = calimeetRecordItemMapper.selectByPrimaryKey(id);
        String userId = calimeetRecordItem.getUserId();
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        if (calimeet == null) {
            return null;
        }
        //维度分层结果，维度得分或者达标率，指标得分/是否达标
        int caliType = calimeet.getCalimeetType();
        String xpdId = calimeet.getXpdId();
        XpdCaliCalcFullDto fullDto = xpdResultCalcService.buildXpdCalcFormula(orgId, xpdId);
        if (fullDto == null) {
            return null;
        }
        CaliDimResultResp resp = new CaliDimResultResp();
        CalimeetDimResultDto resultDto = calimeetRecordMapper.getRecordDetail(orgId, id);
        boolean getScore = fullDto.getResultType() == DimResultTypeEnum.SCORE_VALUE.getCode();
        List<CalimeetResultUserDimPO> caliDumpDimResults = calimeetResultUserDimMapper.getByUserIdDimIds(orgId,
                caliMeetId, Lists.newArrayList(userId),
                BeanCopierUtil.convertList(fullDto.getRuleList(), XpdDimRule4Cali::getSdDimId));
        if (caliType == CaliMeetTypeEnum.LEVEL.getCode()) {
            Map<String, CaliDimResultItemValDto> xpdDimResultMap = IArrayUtils.listAsMap(caliDumpDimResults,
                    CalimeetResultUserDimPO::getSdDimId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedDimResultMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                            .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdDimId,
                    item -> item);
            resp.setGridLevelList(fullDto.getGridLevelList());
            resp.setResultType(CaliResultTypeEnum.TYPE_DIM_LEVEL.getType());
            resp.setItems(BeanCopierUtil.convertList(fullDto.getRuleList(), rule -> {
                CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                itemDto.setSdDimId(rule.getSdDimId());
                itemDto.setItemName(fullDto.getSdDimNameMap().get(rule.getSdDimId()));
                itemDto.setXpdValBean(xpdDimResultMap.get(rule.getSdDimId()));
                itemDto.setCaliValBean(Optional.ofNullable(savedDimResultMap.get(rule.getSdDimId())).map(dimResult -> {
                    CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                    BeanCopierUtil.copy(dimResult, caliVal);
                    return caliVal;
                }).orElse(null));
                caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getGridLevelId, String::toString);
                return itemDto;
            }));
        } else if (caliType == CaliMeetTypeEnum.DIM.getCode()) {
            Map<String, CaliDimResultItemValDto> xpdDimResultMap = IArrayUtils.listAsMap(caliDumpDimResults,
                    CalimeetResultUserDimPO::getSdDimId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedDimResultMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                            .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null), CaliUpdateUserResultDto::getSdDimId,
                    item -> item);
            resp.setResultType(getScore ?
                    CaliResultTypeEnum.TYPE_DIM_SCORE.getType() :
                    CaliResultTypeEnum.TYPE_QUALIFIED_PTG.getType());
            resp.setItems(fullDto.getRuleList().stream()
                    .filter(dimRule -> dimRule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE && !Objects.equals(
                            dimRule.getCalcType(), DimCalcTypeEnum.PERF_RESULT.getCode())).map(rule -> {
                        CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                        itemDto.setSdDimId(rule.getSdDimId());
                        itemDto.setItemName(fullDto.getSdDimNameMap().get(rule.getSdDimId()));
                        itemDto.setXpdValBean(xpdDimResultMap.get(rule.getSdDimId()));
                        itemDto.setCaliValBean(
                                Optional.ofNullable(savedDimResultMap.get(rule.getSdDimId())).map(dimResult -> {
                                    CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                                    BeanCopierUtil.copy(dimResult, caliVal);
                                    return caliVal;
                                }).orElse(null));
                        itemDto.setTotalScore(rule.getTotalScore());
                        caliDimResultItemSetVal(itemDto, getScore ?
                                CaliDimResultItemValDto::getScoreValue :
                                CaliDimResultItemValDto::getQualifiedPtg, BigDecimal::toPlainString);
                        return itemDto;
                    }).collect(Collectors.toList()));
        } else {
            Set<String> indicatorIdSet = new HashSet<>();
            List<XpdDimRule4Cali> useDims = new ArrayList<>();
            fullDto.getRuleList().forEach(rule -> {
                if (rule.getCaliCalcType() != XpdDimRule4Cali.TYPE_NONE && !DimCalcTypeEnum.isPerf(rule.getCalcType())
                        && rule.getFormulaSdIndicatorSet() != null) {
                    indicatorIdSet.addAll(rule.getFormulaSdIndicatorSet());
                    useDims.add(rule);
                }
            });
            Map<String, CaliDimResultItemValDto> xpdIndResultMap = IArrayUtils.listAsMap(
                    calimeetResultUserIndicatorMapper.getByUserIdIndicatorIds(orgId, caliMeetId,
                            Lists.newArrayList(userId), indicatorIdSet),
                    CalimeetResultUserIndicatorPO::getSdIndicatorId, this::convertToXpdItemVal);
            Map<String, CaliUpdateUserResultDto> savedIndResultMap = IArrayUtils.listAsMap(Optional.ofNullable(
                                    CommonUtils.tryParseObject(resultDto.getCaliDetails(), CaliUpdateUserResultWrapDto.class))
                            .map(CaliUpdateUserResultWrapDto::getUserResults).orElse(null),
                    CaliUpdateUserResultDto::getSdIndicatorId, item -> item);
            resp.setResultType(getScore ?
                    CaliResultTypeEnum.TYPE_INDICATOR_SCORE.getType() :
                    CaliResultTypeEnum.TYPE_QUALIFIED.getType());
            resp.setItems(indicatorIdSet.stream().map(sdIndicatorId -> {
                String sdDimId = IArrayUtils.getFirstMatch(useDims,
                        dim -> dim.getFormulaSdIndicatorSet().contains(sdIndicatorId)).getSdDimId();
                CaliDimResultItemDto itemDto = new CaliDimResultItemDto();
                itemDto.setSdDimId(sdDimId);
                itemDto.setSdDimName(fullDto.getSdDimNameMap().get(sdDimId));
                itemDto.setSdIndicatorId(sdIndicatorId);
                XpdSdIndicatorScoreDto scoreDto = fullDto.getSdIndicatorMap().get(sdIndicatorId);
                if (scoreDto != null) {
                    itemDto.setItemName(scoreDto.getName());
                    itemDto.setTotalScore(scoreDto.getTotalScore());
                }
                itemDto.setXpdValBean(xpdIndResultMap.get(sdIndicatorId));
                itemDto.setCaliValBean(Optional.ofNullable(savedIndResultMap.get(sdIndicatorId)).map(dimResult -> {
                    CaliDimResultItemValDto caliVal = new CaliDimResultItemValDto();
                    BeanCopierUtil.copy(dimResult, caliVal);
                    return caliVal;
                }).orElse(null));
                if (getScore) {
                    caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getScoreValue, BigDecimal::toPlainString);
                } else {
                    caliDimResultItemSetVal(itemDto, CaliDimResultItemValDto::getQualified, String::valueOf);
                }
                return itemDto;
            }).collect(Collectors.toList()));
        }
        resp.setCombList(Lists.newArrayList());
        CaliDimResultWrapDto dimResults = CommonUtils.tryParseObject(resultDto.getResultDetails(),
                CaliDimResultWrapDto.class);
        Map<String, CaliDimResultBean> dimResultMap = IArrayUtils.listAsMap(
                Optional.ofNullable(dimResults).map(CaliDimResultWrapDto::getDimResults).orElse(null),
                CaliDimResultDto::getSdDimId, caliDimResult -> {
                    CaliDimResultBean dimResultBean = new CaliDimResultBean();
                    dimResultBean.setSdDimId(caliDimResult.getSdDimId());
                    dimResultBean.setGridLevelId(caliDimResult.getGridLevelId());
                    return dimResultBean;
                });
        caliDumpDimResults.forEach(xpdDimResult -> {
            CaliDimResultBean dimResultBean = dimResultMap.get(xpdDimResult.getSdDimId());
            if (dimResultBean == null) {
                dimResultBean = new CaliDimResultBean();
                dimResultMap.put(xpdDimResult.getSdDimId(), dimResultBean);
            }
            dimResultBean.setXpdGridLevelId(xpdDimResult.getGridLevelId());
            dimResultBean.setXpdScoreValue(xpdDimResult.getScoreValue());
            dimResultBean.setXpdQualifiedPtg(xpdDimResult.getQualifiedPtg());
        });
        resp.setCombList(assembleDimCombResult(fullDto, dimResultMap));
        resp.setXpdLevelId(calimeetResultUserMapper.userLevelId(orgId, caliMeetId, userId));
        resp.setXpdLevelName(IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(),
                ruleLevel -> ruleLevel.getId().equals(resp.getXpdLevelId()), XpdRuleLevelDto::getLevelName));
        resp.setCaliLevelId(Optional.ofNullable(dimResults).map(CaliDimResultWrapDto::getXpdLevelId).orElse(null));
        resp.setCaliLevelName(IArrayUtils.getFirstMatch(fullDto.getXpdRuleLevels(),
                ruleLevel -> ruleLevel.getId().equals(resp.getCaliLevelId()), XpdRuleLevelDto::getLevelName));
        return resp;
    }

    private CaliDimResultItemValDto convertToXpdItemVal(CalimeetResultUserDimPO xpdDimResult) {
        CaliDimResultItemValDto itemVal = new CaliDimResultItemValDto();
        itemVal.setGridLevelId(xpdDimResult.getGridLevelId());
        itemVal.setScoreValue(xpdDimResult.getScoreValue());
        itemVal.setQualifiedPtg(xpdDimResult.getQualifiedPtg());
        itemVal.setPerfLevelId(xpdDimResult.getPerfResultId());
        return itemVal;
    }

    private CaliDimResultItemValDto convertToXpdItemVal(CalimeetResultUserIndicatorPO xpdIndResult) {
        CaliDimResultItemValDto itemVal = new CaliDimResultItemValDto();
        itemVal.setScoreValue(xpdIndResult.getScoreValue());
        itemVal.setQualified(xpdIndResult.getQualified());
        itemVal.setPerfLevelId(xpdIndResult.getPerfResultId());
        return itemVal;
    }

    private <T> void caliDimResultItemSetVal(CaliDimResultItemDto itemDto,
            Function<CaliDimResultItemValDto, T> valGetter, Function<T, String> valToStr) {
        if (itemDto.getXpdValBean() != null) {
            T value = valGetter.apply(itemDto.getXpdValBean());
            if (value != null) {
                itemDto.setXpdVal(valToStr.apply(value));
            }
        }
        if (itemDto.getCaliValBean() != null) {
            T value = valGetter.apply(itemDto.getCaliValBean());
            if (value != null) {
                itemDto.setCaliVal(valToStr.apply(value));
            }
        }
    }

    private List<CaliDimCombResultDto> assembleDimCombResult(XpdCaliCalcFullDto fullDto,
            Map<String, CaliDimResultBean> dimResultMap) {
        List<CaliDimCombResultDto> combList = new ArrayList<>();
        CaliDimResultBean emptyDimResult = new CaliDimResultBean();
        Map<String, Integer> gridLevelIndexMap = StreamUtil.list2map(fullDto.getGridLevelList(),
                XpdGridLevelBriefDto::getId, XpdGridLevelBriefDto::getOrderIndex);
        fullDto.getCombList().forEach(comb -> {
            CaliDimResultBean xDimResult = dimResultMap.getOrDefault(comb.getXSdDimId(), emptyDimResult);
            CaliDimResultBean yDimResult = dimResultMap.getOrDefault(comb.getYSdDimId(), emptyDimResult);
            Integer xpdXLevelIndex = gridLevelIndexMap.get(xDimResult.getXpdGridLevelId());
            Integer xpdYLevelIndex = gridLevelIndexMap.get(yDimResult.getXpdGridLevelId());
            Integer caliXLevelIndex = gridLevelIndexMap.get(xDimResult.getGridLevelId());
            Integer caliYLevelIndex = gridLevelIndexMap.get(yDimResult.getGridLevelId());
            CaliDimCombResultDto combResult = new CaliDimCombResultDto();
            combResult.setCombId(comb.getId());
            combResult.setCombName(comb.getCombName());
            combResult.setXpdCellIndex(
                    GridCellTemplateEnum.getCellIndexByLocation(fullDto.getGridType(), xpdXLevelIndex, xpdYLevelIndex));
            combResult.setXSdDimId(comb.getXSdDimId());
            combResult.setXSdDimName(fullDto.getSdDimNameMap().get(comb.getXSdDimId()));
            combResult.setYSdDimId(comb.getYSdDimId());
            combResult.setYSdDimName(fullDto.getSdDimNameMap().get(comb.getYSdDimId()));
            combResult.setCaliCellIndex(
                    GridCellTemplateEnum.getCellIndexByLocation(fullDto.getGridType(), caliXLevelIndex,
                            caliYLevelIndex));
            if (!CommonUtil.anyNull(xpdXLevelIndex, xpdYLevelIndex, caliXLevelIndex, caliYLevelIndex)) {
                combResult.setCaliGap(
                        Math.abs(caliXLevelIndex - xpdXLevelIndex) + Math.abs(caliYLevelIndex - xpdYLevelIndex));
            }
            combList.add(combResult);
        });
        return combList;
    }

    public boolean validateRecordData(String orgId, String caliMeetId) {
        Long caliCount = calimeetUserMapper.getCaliCount(orgId, caliMeetId);
        return caliCount > 0;
    }

    public void delCaliMeetRecord(String orgId, String caliMeetId) {
        calimeetUserMapper.delCaliRecord(orgId, caliMeetId);
        calimeetRecordMapper.deleteRecordByCaliMeetId(orgId, caliMeetId);
        calimeetRecordItemMapper.deleteRecordByCaliMeetId(orgId, caliMeetId);
    }
}
