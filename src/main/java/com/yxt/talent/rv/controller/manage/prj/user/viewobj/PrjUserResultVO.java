package com.yxt.talent.rv.controller.manage.prj.user.viewobj;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.bifrost.udp.localization.L10NContent;
import com.yxt.bifrost.udp.localization.annotation.L10NProperty;
import com.yxt.bifrost.udp.localization.enums.ShapeEnum;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "人才盘点结果九宫格查询结果")
public class PrjUserResultVO implements L10NContent {

    @Schema(description = "用户id")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.KEY)
    private String userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户姓名")
    @L10NProperty(resourceType = ResourceTypeEnum.USER, shape = ShapeEnum.VALUE)
    private String fullName;

    @Schema(description = "头像url")
    private String imgUrl;

    @JsonProperty("xAxis")
    @Schema(description = "X轴维度id")
    private String xAxis;

    @JsonProperty("xValue")
    @Schema(description = "X轴维度等级（1-3）")
    private int xValue;

    @JsonProperty("xCheckValue")
    @Schema(description = "X校准后轴维度等级（1-3）")
    private int xCheckValue;

    @JsonProperty("yAixs")
    @Schema(description = "y轴维度id")
    private String yAixs;

    @JsonProperty("yValue")
    @Schema(description = "y轴维度等级（1-3）")
    private int yValue;

    @JsonProperty("yCheckValue")
    @Schema(description = "y校准后轴维度等级（1-3）")
    private int yCheckValue;

    @Schema(description = "是否存在下一页：1-是，0-否")
    private int page = 0;

    @Schema(description = "总人数")
    private Long totalUser;

    @Schema(description = "百分比")
    private String percentStr;

    @Schema(description = "校准幅度")
    private Integer caliShift;

    @Schema(description = "盘点人员维度结果")
    private List<XpdUserDimResultVO> userDimResults = new ArrayList<>();

}
